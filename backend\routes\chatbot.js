const express = require('express');
const router = express.Router();
const ChatbotQuery = require('../models/ChatbotQuery');
const ChatbotService = require('../services/chatbotService');
const { auth } = require('../middleware/auth');

const chatbotService = new ChatbotService();

// Process a chatbot query
router.post('/query', auth, async (req, res) => {
  try {
    const { query, session_id, user_id } = req.body;
    
    if (!query || !session_id) {
      return res.status(400).json({
        success: false,
        error: 'Query text and session ID are required'
      });
    }

    console.log(`Processing chatbot query: "${query}" for session: ${session_id}`);
    
    const result = await chatbotService.processQuery(query, session_id, user_id || req.user?.id);
    
    res.json({
      success: true,
      ...result
    });
  } catch (error) {
    console.error('Error processing chatbot query:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Get session conversation history
router.get('/sessions/:sessionId/history', auth, async (req, res) => {
  try {
    const { limit = 10 } = req.query;
    
    const history = await chatbotService.getSessionHistory(req.params.sessionId, parseInt(limit));
    
    res.json({
      session_id: req.params.sessionId,
      history,
      total_queries: history.length
    });
  } catch (error) {
    console.error('Error fetching session history:', error);
    res.status(500).json({ error: error.message });
  }
});

// Get user conversation history
router.get('/users/:userId/history', auth, async (req, res) => {
  try {
    const { limit = 20 } = req.query;
    
    const history = await chatbotService.getUserHistory(req.params.userId, parseInt(limit));
    
    res.json({
      user_id: req.params.userId,
      history,
      total_queries: history.length
    });
  } catch (error) {
    console.error('Error fetching user history:', error);
    res.status(500).json({ error: error.message });
  }
});

// Submit user feedback for a query
router.post('/queries/:queryId/feedback', auth, async (req, res) => {
  try {
    const { rating, feedback, is_helpful } = req.body;
    
    if (!rating || rating < 1 || rating > 5) {
      return res.status(400).json({
        success: false,
        error: 'Rating must be between 1 and 5'
      });
    }

    const query = await ChatbotQuery.findOne({ query_id: req.params.queryId });
    
    if (!query) {
      return res.status(404).json({
        success: false,
        error: 'Query not found'
      });
    }

    await query.addUserFeedback(rating, feedback, is_helpful);
    
    res.json({
      success: true,
      message: 'Feedback submitted successfully',
      query_id: req.params.queryId
    });
  } catch (error) {
    console.error('Error submitting feedback:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Get chatbot analytics
router.get('/analytics', auth, async (req, res) => {
  try {
    const { days = 30 } = req.query;
    
    const analytics = await chatbotService.getAnalytics(parseInt(days));
    
    res.json(analytics);
  } catch (error) {
    console.error('Error fetching chatbot analytics:', error);
    res.status(500).json({ error: error.message });
  }
});

// Get popular queries
router.get('/analytics/popular-queries', auth, async (req, res) => {
  try {
    const { days = 7, limit = 10 } = req.query;
    
    const popularQueries = await ChatbotQuery.getPopularQueries(parseInt(days), parseInt(limit));
    
    res.json({
      popular_queries: popularQueries,
      analysis_period_days: parseInt(days)
    });
  } catch (error) {
    console.error('Error fetching popular queries:', error);
    res.status(500).json({ error: error.message });
  }
});

// Get failed queries for debugging
router.get('/analytics/failed-queries', auth, async (req, res) => {
  try {
    const { limit = 20 } = req.query;
    
    const failedQueries = await ChatbotQuery.getFailedQueries(parseInt(limit));
    
    res.json({
      failed_queries: failedQueries,
      total_failed: failedQueries.length
    });
  } catch (error) {
    console.error('Error fetching failed queries:', error);
    res.status(500).json({ error: error.message });
  }
});

// Get query analytics by type
router.get('/analytics/query-types', auth, async (req, res) => {
  try {
    const { days = 30 } = req.query;
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - parseInt(days));
    
    const queryTypeAnalytics = await ChatbotQuery.aggregate([
      {
        $match: {
          createdAt: { $gte: startDate },
          status: 'completed'
        }
      },
      {
        $group: {
          _id: '$query_type',
          count: { $sum: 1 },
          avg_confidence: { $avg: '$confidence_score' },
          avg_processing_time: { $avg: '$processing_time_ms' },
          avg_rating: { $avg: '$user_rating' }
        }
      },
      { $sort: { count: -1 } }
    ]);
    
    res.json({
      query_type_analytics: queryTypeAnalytics,
      analysis_period_days: parseInt(days)
    });
  } catch (error) {
    console.error('Error fetching query type analytics:', error);
    res.status(500).json({ error: error.message });
  }
});

// Get chatbot performance metrics
router.get('/analytics/performance', auth, async (req, res) => {
  try {
    const { days = 30 } = req.query;
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - parseInt(days));
    
    const [
      totalQueries,
      completedQueries,
      failedQueries,
      avgProcessingTime,
      avgConfidence,
      avgRating
    ] = await Promise.all([
      ChatbotQuery.countDocuments({ createdAt: { $gte: startDate } }),
      ChatbotQuery.countDocuments({ 
        createdAt: { $gte: startDate }, 
        status: 'completed' 
      }),
      ChatbotQuery.countDocuments({ 
        createdAt: { $gte: startDate }, 
        status: { $in: ['failed', 'timeout'] } 
      }),
      ChatbotQuery.aggregate([
        { $match: { createdAt: { $gte: startDate }, status: 'completed' } },
        { $group: { _id: null, avgTime: { $avg: '$processing_time_ms' } } }
      ]),
      ChatbotQuery.aggregate([
        { $match: { createdAt: { $gte: startDate }, status: 'completed' } },
        { $group: { _id: null, avgConfidence: { $avg: '$confidence_score' } } }
      ]),
      ChatbotQuery.aggregate([
        { $match: { createdAt: { $gte: startDate }, user_rating: { $exists: true } } },
        { $group: { _id: null, avgRating: { $avg: '$user_rating' } } }
      ])
    ]);
    
    res.json({
      performance_metrics: {
        total_queries: totalQueries,
        completed_queries: completedQueries,
        failed_queries: failedQueries,
        success_rate: totalQueries > 0 ? Math.round((completedQueries / totalQueries) * 100) : 0,
        avg_processing_time_ms: avgProcessingTime[0]?.avgTime || 0,
        avg_confidence_score: avgConfidence[0]?.avgConfidence || 0,
        avg_user_rating: avgRating[0]?.avgRating || 0
      },
      analysis_period_days: parseInt(days)
    });
  } catch (error) {
    console.error('Error fetching performance metrics:', error);
    res.status(500).json({ error: error.message });
  }
});

// Get queries with filtering and pagination
router.get('/queries', auth, async (req, res) => {
  try {
    const {
      page = 1,
      limit = 20,
      session_id,
      user_id,
      query_type,
      status,
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = req.query;

    const query = {};
    if (session_id) query.session_id = session_id;
    if (user_id) query.user_id = user_id;
    if (query_type) query.query_type = query_type;
    if (status) query.status = status;

    const sort = {};
    sort[sortBy] = sortOrder === 'desc' ? -1 : 1;

    const queries = await ChatbotQuery.find(query)
      .sort(sort)
      .limit(limit * 1)
      .skip((page - 1) * limit)
      .select('query_id query_text response_text query_type status confidence_score user_rating createdAt processing_time_ms');

    const total = await ChatbotQuery.countDocuments(query);

    res.json({
      queries,
      pagination: {
        current_page: parseInt(page),
        total_pages: Math.ceil(total / limit),
        total_items: total,
        items_per_page: parseInt(limit)
      }
    });
  } catch (error) {
    console.error('Error fetching queries:', error);
    res.status(500).json({ error: error.message });
  }
});

// Get specific query by ID
router.get('/queries/:queryId', auth, async (req, res) => {
  try {
    const query = await ChatbotQuery.findOne({ query_id: req.params.queryId });
    
    if (!query) {
      return res.status(404).json({ error: 'Query not found' });
    }
    
    res.json(query);
  } catch (error) {
    console.error('Error fetching query:', error);
    res.status(500).json({ error: error.message });
  }
});

// Get chatbot dashboard summary
router.get('/dashboard/summary', auth, async (req, res) => {
  try {
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);
    const lastWeek = new Date(today);
    lastWeek.setDate(lastWeek.getDate() - 7);

    const [
      totalQueries,
      todayQueries,
      weekQueries,
      avgConfidence,
      topQueryTypes,
      recentQueries
    ] = await Promise.all([
      ChatbotQuery.countDocuments(),
      ChatbotQuery.countDocuments({ 
        createdAt: { $gte: yesterday } 
      }),
      ChatbotQuery.countDocuments({ 
        createdAt: { $gte: lastWeek } 
      }),
      ChatbotQuery.aggregate([
        { $match: { status: 'completed' } },
        { $group: { _id: null, avgConfidence: { $avg: '$confidence_score' } } }
      ]),
      ChatbotQuery.aggregate([
        { $match: { createdAt: { $gte: lastWeek } } },
        {
          $group: {
            _id: '$query_type',
            count: { $sum: 1 }
          }
        },
        { $sort: { count: -1 } },
        { $limit: 5 }
      ]),
      ChatbotQuery.find()
        .sort({ createdAt: -1 })
        .limit(5)
        .select('query_id query_text response_text status confidence_score createdAt')
    ]);

    res.json({
      summary: {
        total_queries: totalQueries,
        today_queries: todayQueries,
        week_queries: weekQueries,
        avg_confidence: avgConfidence[0]?.avgConfidence || 0
      },
      top_query_types: topQueryTypes,
      recent_queries: recentQueries,
      last_updated: new Date()
    });
  } catch (error) {
    console.error('Error fetching chatbot dashboard summary:', error);
    res.status(500).json({ error: error.message });
  }
});

// Health check endpoint
router.get('/health', (req, res) => {
  res.json({
    status: 'OK',
    service: 'Chatbot Service',
    timestamp: new Date().toISOString()
  });
});

module.exports = router;