const AmazonReview = require('../models/AmazonReview');
const AmazonProduct = require('../models/AmazonProduct');
const InstacartProduct = require('../models/InstacartProduct');
const InstacartOrderProduct = require('../models/InstacartOrderProduct');
const ChatbotQuery = require('../models/ChatbotQuery');
const DemandForecast = require('../models/DemandForecast');
const Product = require('../models/Product');

class ChatbotService {
  constructor() {
    this.intentPatterns = {
      product_search: [
        /find.*product/i,
        /search.*for/i,
        /looking.*for/i,
        /show.*me.*products/i,
        /what.*products/i
      ],
      review_analysis: [
        /reviews.*for/i,
        /what.*people.*say/i,
        /customer.*reviews/i,
        /feedback.*about/i,
        /opinions.*on/i
      ],
      sentiment_analysis: [
        /sentiment.*about/i,
        /how.*people.*feel/i,
        /positive.*negative/i,
        /customer.*satisfaction/i,
        /rating.*analysis/i
      ],
      recommendation: [
        /recommend/i,
        /suggest/i,
        /similar.*products/i,
        /alternatives/i,
        /what.*should.*buy/i
      ],
      comparison: [
        /compare/i,
        /versus/i,
        /vs/i,
        /difference.*between/i,
        /which.*better/i
      ],
      forecast_demand: [
        /forecast/i,
        /predict.*demand/i,
        /future.*sales/i,
        /demand.*prediction/i,
        /sales.*forecast/i
      ],
      check_inventory: [
        /inventory/i,
        /stock.*level/i,
        /how.*many.*in.*stock/i,
        /availability/i,
        /reorder/i
      ]
    };

    this.entityPatterns = {
      product: /\b([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)\b/g,
      brand: /\b(brand|made by|from)\s+([A-Z][a-z]+)\b/gi,
      category: /\b(category|type|kind)\s+([a-z]+)\b/gi,
      rating: /\b([1-5])\s*star/gi,
      sentiment: /\b(positive|negative|neutral|good|bad)\b/gi,
      price: /\$(\d+(?:\.\d{2})?)/g
    };
  }

  /**
   * Process a chatbot query
   */
  async processQuery(queryText, sessionId, userId = null) {
    const startTime = Date.now();
    
    try {
      // Create query record
      const query = new ChatbotQuery({
        query_id: `query_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        query_text: queryText,
        session_id: sessionId,
        user_id: userId,
        status: 'processing'
      });

      // Process the query
      const processedQuery = this.preprocessQuery(queryText);
      query.processed_query = processedQuery;

      // Extract entities and intent
      const entities = this.extractEntities(queryText);
      const intent = this.detectIntent(queryText);

      query.extracted_entities = entities;
      query.query_intent = this.mapIntentToQueryIntent(intent);
      query.query_type = this.getQueryType(intent);

      // Generate response based on intent
      const response = await this.generateResponse(intent, entities, processedQuery, query);
      
      // Update query with response
      query.response_text = response.text;
      query.response_data = response.data;
      query.response_type = response.type;
      query.confidence_score = response.confidence;
      query.updateProcessingTime(startTime);
      query.status = 'completed';

      await query.save();

      return {
        query_id: query.query_id,
        response: response.text,
        response_data: response.data,
        response_type: response.type,
        confidence: response.confidence,
        processing_time: query.processing_time_ms
      };

    } catch (error) {
      console.error('Error processing chatbot query:', error);
      
      // Save failed query with all required fields
      const failedQuery = new ChatbotQuery({
        query_id: `query_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        query_text: queryText,
        session_id: sessionId,
        user_id: userId,
        query_type: 'general_question', // Default query type for failed queries
        query_intent: 'get_statistics', // Default query intent for failed queries
        response_text: "I'm sorry, I encountered an error processing your request. Please try again or rephrase your question.",
        status: 'failed',
        error_message: error.message,
        processing_time_ms: Date.now() - startTime,
        confidence_score: 0,
        response_type: 'text'
      });
      
      try {
        await failedQuery.save();
      } catch (saveError) {
        console.error('Error saving failed query:', saveError);
      }

      return {
        query_id: failedQuery.query_id,
        response: "I'm sorry, I encountered an error processing your request. Please try again or rephrase your question.",
        response_type: 'text',
        confidence: 0,
        error: error.message
      };
    }
  }

  /**
   * Preprocess query text
   */
  preprocessQuery(queryText) {
    return queryText
      .toLowerCase()
      .trim()
      .replace(/[^\w\s]/g, ' ')
      .replace(/\s+/g, ' ');
  }

  /**
   * Extract entities from query text
   */
  extractEntities(queryText) {
    const entities = [];

    Object.entries(this.entityPatterns).forEach(([entityType, pattern]) => {
      const matches = queryText.matchAll(pattern);
      for (const match of matches) {
        entities.push({
          entity_type: entityType,
          entity_value: match[1] || match[0],
          confidence: 0.8
        });
      }
    });

    return entities;
  }

  /**
   * Detect intent from query text
   */
  detectIntent(queryText) {
    let bestIntent = 'general_question';
    let maxMatches = 0;

    Object.entries(this.intentPatterns).forEach(([intent, patterns]) => {
      const matches = patterns.reduce((count, pattern) => {
        return count + (pattern.test(queryText) ? 1 : 0);
      }, 0);

      if (matches > maxMatches) {
        maxMatches = matches;
        bestIntent = intent;
      }
    });

    return bestIntent;
  }

  /**
   * Get query type from intent
   */
  getQueryType(intent) {
    const intentToTypeMap = {
      product_search: 'product_search',
      review_analysis: 'review_analysis',
      sentiment_analysis: 'sentiment_analysis',
      recommendation: 'recommendation',
      comparison: 'comparison',
      forecast_demand: 'demand_forecast',
      check_inventory: 'inventory_query',
      general_question: 'general_question'
    };

    return intentToTypeMap[intent] || 'general_question';
  }

  /**
   * Map intent to query_intent enum values
   */
  mapIntentToQueryIntent(intent) {
    const intentMap = {
      product_search: 'find_product',
      review_analysis: 'get_reviews',
      sentiment_analysis: 'analyze_sentiment',
      recommendation: 'get_recommendations',
      comparison: 'compare_products',
      forecast_demand: 'forecast_demand',
      check_inventory: 'check_inventory',
      general_question: 'get_statistics'
    };

    return intentMap[intent] || 'get_statistics';
  }

  /**
   * Generate response based on intent and entities
   */
  async generateResponse(intent, entities, processedQuery, queryRecord) {
    switch (intent) {
      case 'product_search':
        return await this.handleProductSearch(entities, processedQuery, queryRecord);
      
      case 'review_analysis':
        return await this.handleReviewAnalysis(entities, processedQuery, queryRecord);
      
      case 'sentiment_analysis':
        return await this.handleSentimentAnalysis(entities, processedQuery, queryRecord);
      
      case 'recommendation':
        return await this.handleRecommendation(entities, processedQuery, queryRecord);
      
      case 'comparison':
        return await this.handleComparison(entities, processedQuery, queryRecord);
      
      case 'forecast_demand':
        return await this.handleDemandForecast(entities, processedQuery, queryRecord);
      
      case 'check_inventory':
        return await this.handleInventoryQuery(entities, processedQuery, queryRecord);
      
      default:
        return await this.handleGeneralQuestion(entities, processedQuery, queryRecord);
    }
  }

  /**
   * Handle product search queries
   */
  async handleProductSearch(entities, processedQuery, queryRecord) {
    try {
      const searchTerms = entities
        .filter(e => e.entity_type === 'product')
        .map(e => e.entity_value)
        .join(' ');

      const searchQuery = searchTerms || processedQuery;
      
      // Search in Amazon products
      const amazonProducts = await AmazonProduct.searchProducts(searchQuery, 5);
      
      // Search in Instacart products
      const instacartProducts = await InstacartProduct.find({
        product_name: new RegExp(searchQuery, 'i')
      }).limit(5);

      queryRecord.addDataSource('amazon_products', `searchProducts("${searchQuery}")`, amazonProducts.length);
      queryRecord.addDataSource('instacart_products', `find product_name regex`, instacartProducts.length);

      const responseData = {
        amazon_products: amazonProducts.map(p => ({
          id: p.product_id,
          title: p.title,
          rating: p.average_rating,
          reviews: p.total_reviews,
          sentiment: p.overall_sentiment
        })),
        instacart_products: instacartProducts.map(p => ({
          id: p.product_id,
          name: p.product_name,
          aisle: p.aisle_id,
          department: p.department_id
        }))
      };

      let responseText = `I found ${amazonProducts.length + instacartProducts.length} products matching "${searchQuery}":\n\n`;
      
      if (amazonProducts.length > 0) {
        responseText += "**Amazon Fine Food Products:**\n";
        amazonProducts.forEach((product, index) => {
          responseText += `${index + 1}. ${product.title} - ${product.average_rating}⭐ (${product.total_reviews} reviews)\n`;
        });
        responseText += "\n";
      }

      if (instacartProducts.length > 0) {
        responseText += "**Instacart Products:**\n";
        instacartProducts.forEach((product, index) => {
          responseText += `${index + 1}. ${product.product_name}\n`;
        });
      }

      return {
        text: responseText,
        data: responseData,
        type: 'structured_data',
        confidence: 0.9
      };

    } catch (error) {
      console.error('Error in product search:', error);
      return {
        text: "I encountered an error while searching for products. Please try again.",
        data: null,
        type: 'text',
        confidence: 0.1
      };
    }
  }

  /**
   * Handle review analysis queries
   */
  async handleReviewAnalysis(entities, processedQuery, queryRecord) {
    try {
      const productEntity = entities.find(e => e.entity_type === 'product');
      
      if (!productEntity) {
        return {
          text: "Please specify which product you'd like to see reviews for.",
          data: null,
          type: 'text',
          confidence: 0.3
        };
      }

      const productName = productEntity.entity_value;
      
      // Find product
      const product = await AmazonProduct.findOne({
        title: new RegExp(productName, 'i')
      });

      if (!product) {
        return {
          text: `I couldn't find a product matching "${productName}". Please try a different search term.`,
          data: null,
          type: 'text',
          confidence: 0.2
        };
      }

      // Get recent reviews
      const reviews = await AmazonReview.getReviewsByProduct(product.product_id, 10);
      
      queryRecord.addDataSource('amazon_reviews', `getReviewsByProduct("${product.product_id}")`, reviews.length);

      const responseData = {
        product: {
          id: product.product_id,
          title: product.title,
          rating: product.average_rating,
          total_reviews: product.total_reviews
        },
        reviews: reviews.map(r => ({
          id: r.review_id,
          rating: r.score,
          summary: r.summary,
          text: r.text.substring(0, 200) + '...',
          sentiment: r.sentiment_label,
          date: r.time
        }))
      };

      let responseText = `Here are recent reviews for ${product.title}:\n\n`;
      responseText += `Overall Rating: ${product.average_rating}⭐ (${product.total_reviews} total reviews)\n\n`;
      
      reviews.slice(0, 5).forEach((review, index) => {
        responseText += `**Review ${index + 1}** (${review.score}⭐ - ${review.sentiment_label}):\n`;
        responseText += `"${review.summary}"\n`;
        responseText += `${review.text.substring(0, 150)}...\n\n`;
      });

      return {
        text: responseText,
        data: responseData,
        type: 'structured_data',
        confidence: 0.95
      };

    } catch (error) {
      console.error('Error in review analysis:', error);
      return {
        text: "I encountered an error while analyzing reviews. Please try again.",
        data: null,
        type: 'text',
        confidence: 0.1
      };
    }
  }

  /**
   * Handle sentiment analysis queries
   */
  async handleSentimentAnalysis(entities, processedQuery, queryRecord) {
    try {
      const productEntity = entities.find(e => e.entity_type === 'product');
      
      if (!productEntity) {
        return {
          text: "Please specify which product you'd like sentiment analysis for.",
          data: null,
          type: 'text',
          confidence: 0.3
        };
      }

      const productName = productEntity.entity_value;
      
      // Find product
      const product = await AmazonProduct.findOne({
        title: new RegExp(productName, 'i')
      });

      if (!product) {
        return {
          text: `I couldn't find a product matching "${productName}".`,
          data: null,
          type: 'text',
          confidence: 0.2
        };
      }

      // Get sentiment analysis
      const sentimentAnalysis = await AmazonReview.getProductSentimentAnalysis(product.product_id);
      
      queryRecord.addDataSource('amazon_reviews', `getProductSentimentAnalysis("${product.product_id}")`, sentimentAnalysis.length);

      const responseData = {
        product: {
          id: product.product_id,
          title: product.title,
          overall_sentiment: product.overall_sentiment
        },
        sentiment_breakdown: sentimentAnalysis,
        sentiment_distribution: product.sentiment_distribution
      };

      let responseText = `Sentiment Analysis for ${product.title}:\n\n`;
      responseText += `Overall Sentiment: ${product.overall_sentiment.toUpperCase()}\n\n`;
      responseText += `Sentiment Distribution:\n`;
      responseText += `• Positive: ${product.sentiment_distribution.positive} reviews\n`;
      responseText += `• Neutral: ${product.sentiment_distribution.neutral} reviews\n`;
      responseText += `• Negative: ${product.sentiment_distribution.negative} reviews\n\n`;

      if (product.strengths && product.strengths.length > 0) {
        responseText += `**Strengths:** ${product.strengths.slice(0, 5).join(', ')}\n`;
      }

      if (product.weaknesses && product.weaknesses.length > 0) {
        responseText += `**Areas for Improvement:** ${product.weaknesses.slice(0, 5).join(', ')}\n`;
      }

      return {
        text: responseText,
        data: responseData,
        type: 'chart',
        confidence: 0.9
      };

    } catch (error) {
      console.error('Error in sentiment analysis:', error);
      return {
        text: "I encountered an error while analyzing sentiment. Please try again.",
        data: null,
        type: 'text',
        confidence: 0.1
      };
    }
  }

  /**
   * Handle recommendation queries
   */
  async handleRecommendation(entities, processedQuery, queryRecord) {
    try {
      // Get top-rated products
      const topProducts = await AmazonProduct.getTopRatedProducts(5);
      
      queryRecord.addDataSource('amazon_products', 'getTopRatedProducts(5)', topProducts.length);

      const responseData = {
        recommendations: topProducts.map(p => ({
          id: p.product_id,
          title: p.title,
          rating: p.average_rating,
          reviews: p.total_reviews,
          sentiment: p.overall_sentiment,
          recommendation_score: p.recommendation_score
        }))
      };

      let responseText = "Here are my top product recommendations based on ratings and reviews:\n\n";
      
      topProducts.forEach((product, index) => {
        responseText += `${index + 1}. **${product.title}**\n`;
        responseText += `   Rating: ${product.average_rating}⭐ (${product.total_reviews} reviews)\n`;
        responseText += `   Sentiment: ${product.overall_sentiment}\n`;
        responseText += `   Recommendation Score: ${Math.round(product.recommendation_score * 100)}%\n\n`;
      });

      return {
        text: responseText,
        data: responseData,
        type: 'recommendation_list',
        confidence: 0.85
      };

    } catch (error) {
      console.error('Error in recommendations:', error);
      return {
        text: "I encountered an error while generating recommendations. Please try again.",
        data: null,
        type: 'text',
        confidence: 0.1
      };
    }
  }

  /**
   * Handle comparison queries
   */
  async handleComparison(entities, processedQuery, queryRecord) {
    try {
      const productEntities = entities.filter(e => e.entity_type === 'product');
      
      if (productEntities.length < 2) {
        return {
          text: "Please specify at least two products to compare.",
          data: null,
          type: 'text',
          confidence: 0.3
        };
      }

      const products = [];
      for (const entity of productEntities.slice(0, 2)) {
        const product = await AmazonProduct.findOne({
          title: new RegExp(entity.entity_value, 'i')
        });
        if (product) products.push(product);
      }

      if (products.length < 2) {
        return {
          text: "I couldn't find enough products to compare. Please check the product names.",
          data: null,
          type: 'text',
          confidence: 0.2
        };
      }

      queryRecord.addDataSource('amazon_products', 'product comparison', products.length);

      const responseData = {
        comparison: products.map(p => ({
          id: p.product_id,
          title: p.title,
          rating: p.average_rating,
          reviews: p.total_reviews,
          sentiment: p.overall_sentiment,
          strengths: p.strengths,
          weaknesses: p.weaknesses
        }))
      };

      let responseText = `Comparison between ${products[0].title} and ${products[1].title}:\n\n`;
      
      products.forEach((product, index) => {
        responseText += `**Product ${index + 1}: ${product.title}**\n`;
        responseText += `Rating: ${product.average_rating}⭐ (${product.total_reviews} reviews)\n`;
        responseText += `Sentiment: ${product.overall_sentiment}\n`;
        if (product.strengths && product.strengths.length > 0) {
          responseText += `Strengths: ${product.strengths.slice(0, 3).join(', ')}\n`;
        }
        if (product.weaknesses && product.weaknesses.length > 0) {
          responseText += `Weaknesses: ${product.weaknesses.slice(0, 3).join(', ')}\n`;
        }
        responseText += '\n';
      });

      return {
        text: responseText,
        data: responseData,
        type: 'table',
        confidence: 0.8
      };

    } catch (error) {
      console.error('Error in comparison:', error);
      return {
        text: "I encountered an error while comparing products. Please try again.",
        data: null,
        type: 'text',
        confidence: 0.1
      };
    }
  }

  /**
   * Handle demand forecast queries
   */
  async handleDemandForecast(entities, processedQuery, queryRecord) {
    try {
      const productEntity = entities.find(e => e.entity_type === 'product');
      
      if (!productEntity) {
        // Get general forecast summary
        const forecasts = await DemandForecast.find({ status: 'active' })
          .sort({ forecast_generated_at: -1 })
          .limit(5);

        queryRecord.addDataSource('demand_forecasts', 'find active forecasts', forecasts.length);

        let responseText = "Here are recent demand forecasts:\n\n";
        
        forecasts.forEach((forecast, index) => {
          const avgDemand = forecast.getAveragePredictedDemand(7);
          responseText += `${index + 1}. ${forecast.product_name}\n`;
          responseText += `   Avg Daily Demand: ${Math.round(avgDemand)} units\n`;
          responseText += `   Forecast Period: ${forecast.forecast_horizon_days} days\n\n`;
        });

        return {
          text: responseText,
          data: { forecasts },
          type: 'structured_data',
          confidence: 0.7
        };
      }

      // Find specific product forecast
      const productName = productEntity.entity_value;
      const forecast = await DemandForecast.findOne({
        product_name: new RegExp(productName, 'i'),
        status: 'active'
      });

      if (!forecast) {
        return {
          text: `I couldn't find demand forecast data for "${productName}".`,
          data: null,
          type: 'text',
          confidence: 0.2
        };
      }

      queryRecord.addDataSource('demand_forecasts', `find forecast for "${productName}"`, 1);

      const avgDemand7 = forecast.getAveragePredictedDemand(7);
      const avgDemand30 = forecast.getAveragePredictedDemand(30);

      const responseData = {
        product: forecast.product_name,
        forecast_data: forecast.forecast_data.slice(0, 30),
        avg_demand_7_days: avgDemand7,
        avg_demand_30_days: avgDemand30
      };

      let responseText = `Demand Forecast for ${forecast.product_name}:\n\n`;
      responseText += `Average Daily Demand (7 days): ${Math.round(avgDemand7)} units\n`;
      responseText += `Average Daily Demand (30 days): ${Math.round(avgDemand30)} units\n`;
      responseText += `Forecast Model: ${forecast.model_type}\n`;
      responseText += `Last Updated: ${forecast.forecast_generated_at.toLocaleDateString()}\n`;

      return {
        text: responseText,
        data: responseData,
        type: 'chart',
        confidence: 0.9
      };

    } catch (error) {
      console.error('Error in demand forecast:', error);
      return {
        text: "I encountered an error while retrieving demand forecasts. Please try again.",
        data: null,
        type: 'text',
        confidence: 0.1
      };
    }
  }

  /**
   * Handle inventory queries
   */
  async handleInventoryQuery(entities, processedQuery, queryRecord) {
    try {
      const productEntity = entities.find(e => e.entity_type === 'product');
      
      if (!productEntity) {
        // Get general inventory summary
        const products = await Product.find({})
          .sort({ quantity: 1 })
          .limit(10);

        queryRecord.addDataSource('inventory', 'find products sorted by quantity', products.length);

        let responseText = "Current inventory levels (lowest stock first):\n\n";
        
        products.forEach((product, index) => {
          responseText += `${index + 1}. ${product.name}\n`;
          responseText += `   Stock: ${product.quantity} units\n`;
          responseText += `   Min Stock: ${product.minStock} units\n`;
          responseText += `   Status: ${product.quantity <= product.minStock ? '⚠️ Low Stock' : '✅ In Stock'}\n\n`;
        });

        return {
          text: responseText,
          data: { products },
          type: 'structured_data',
          confidence: 0.8
        };
      }

      // Find specific product
      const productName = productEntity.entity_value;
      const product = await Product.findOne({
        name: new RegExp(productName, 'i')
      });

      if (!product) {
        return {
          text: `I couldn't find inventory information for "${productName}".`,
          data: null,
          type: 'text',
          confidence: 0.2
        };
      }

      queryRecord.addDataSource('inventory', `find product "${productName}"`, 1);

      const responseData = {
        product: {
          name: product.name,
          sku: product.sku,
          quantity: product.quantity,
          min_stock: product.minStock,
          max_stock: product.maxStock,
          status: product.quantity <= product.minStock ? 'low_stock' : 'in_stock'
        }
      };

      let responseText = `Inventory Status for ${product.name}:\n\n`;
      responseText += `SKU: ${product.sku}\n`;
      responseText += `Current Stock: ${product.quantity} units\n`;
      responseText += `Minimum Stock Level: ${product.minStock} units\n`;
      responseText += `Maximum Stock Level: ${product.maxStock} units\n`;
      responseText += `Status: ${product.quantity <= product.minStock ? '⚠️ Low Stock - Reorder Needed' : '✅ In Stock'}\n`;

      return {
        text: responseText,
        data: responseData,
        type: 'structured_data',
        confidence: 0.95
      };

    } catch (error) {
      console.error('Error in inventory query:', error);
      return {
        text: "I encountered an error while checking inventory. Please try again.",
        data: null,
        type: 'text',
        confidence: 0.1
      };
    }
  }

  /**
   * Handle general questions
   */
  async handleGeneralQuestion(entities, processedQuery, queryRecord) {
    const responses = [
      "I can help you with product searches, review analysis, sentiment analysis, recommendations, demand forecasting, and inventory queries. What would you like to know?",
      "I have access to Amazon Fine Food Reviews and Instacart market data. You can ask me about products, reviews, forecasts, or inventory levels.",
      "Try asking me things like: 'Find products similar to coffee', 'What are the reviews for organic honey?', 'Show me demand forecast for pasta', or 'Check inventory levels'."
    ];

    return {
      text: responses[Math.floor(Math.random() * responses.length)],
      data: null,
      type: 'text',
      confidence: 0.5
    };
  }

  /**
   * Get session history
   */
  async getSessionHistory(sessionId, limit = 10) {
    try {
      return await ChatbotQuery.getSessionHistory(sessionId, limit);
    } catch (error) {
      console.error('Error getting session history:', error);
      return [];
    }
  }

  /**
   * Get user history
   */
  async getUserHistory(userId, limit = 20) {
    try {
      return await ChatbotQuery.getUserHistory(userId, limit);
    } catch (error) {
      console.error('Error getting user history:', error);
      return [];
    }
  }

  /**
   * Get chatbot analytics
   */
  async getAnalytics(days = 30) {
    try {
      const [queryAnalytics, popularQueries, failedQueries] = await Promise.all([
        ChatbotQuery.getQueryAnalytics(days),
        ChatbotQuery.getPopularQueries(days),
        ChatbotQuery.getFailedQueries(20)
      ]);

      return {
        query_analytics: queryAnalytics,
        popular_queries: popularQueries,
        failed_queries: failedQueries,
        analysis_period_days: days
      };
    } catch (error) {
      console.error('Error getting chatbot analytics:', error);
      throw error;
    }
  }
}

module.exports = ChatbotService;