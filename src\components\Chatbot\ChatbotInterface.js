import React, { useState, useEffect, useRef } from 'react';
import { 
  MessageSquare, 
  Send, 
  Bot, 
  User, 
  Loader, 
  ThumbsUp, 
  ThumbsDown,
  BarChart3,
  Star,
  Package,
  TrendingUp
} from 'lucide-react';
import { chatbotAPI } from '../../services/api';
import toast from 'react-hot-toast';

const ChatbotInterface = () => {
  const [messages, setMessages] = useState([]);
  const [inputMessage, setInputMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [sessionId] = useState(`session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`);
  const messagesEndRef = useRef(null);

  useEffect(() => {
    // Add welcome message
    setMessages([
      {
        id: 'welcome',
        type: 'bot',
        content: "Hello! I'm your AI assistant for inventory management and product analysis. I can help you with:\n\n• Product searches and recommendations\n• Review analysis and sentiment insights\n• Demand forecasting\n• Inventory queries\n• Market basket analysis\n\nWhat would you like to know?",
        timestamp: new Date(),
        suggestions: [
          "Find products similar to coffee",
          "What are the reviews for organic products?",
          "Show me demand forecast for popular items",
          "Check inventory levels for low stock items"
        ]
      }
    ]);
  }, []);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const handleSendMessage = async (messageText = inputMessage) => {
    if (!messageText.trim() || isLoading) return;

    const userMessage = {
      id: `user_${Date.now()}`,
      type: 'user',
      content: messageText,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputMessage('');
    setIsLoading(true);

    try {
      const response = await chatbotAPI.processQuery({
        query: messageText,
        session_id: sessionId
      });

      const botMessage = {
        id: response.data.query_id,
        type: 'bot',
        content: response.data.response,
        timestamp: new Date(),
        confidence: response.data.confidence,
        response_data: response.data.response_data,
        response_type: response.data.response_type,
        processing_time: response.data.processing_time
      };

      setMessages(prev => [...prev, botMessage]);
    } catch (error) {
      console.error('Error sending message:', error);
      const errorMessage = {
        id: `error_${Date.now()}`,
        type: 'bot',
        content: "I'm sorry, I encountered an error processing your request. Please try again or rephrase your question.",
        timestamp: new Date(),
        isError: true
      };
      setMessages(prev => [...prev, errorMessage]);
      toast.error('Failed to process message');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSuggestionClick = (suggestion) => {
    handleSendMessage(suggestion);
  };

  const handleFeedback = async (messageId, isPositive) => {
    try {
      await chatbotAPI.submitFeedback(messageId, {
        rating: isPositive ? 5 : 1,
        is_helpful: isPositive
      });
      
      setMessages(prev => prev.map(msg => 
        msg.id === messageId 
          ? { ...msg, feedback: isPositive ? 'positive' : 'negative' }
          : msg
      ));
      
      toast.success('Thank you for your feedback!');
    } catch (error) {
      console.error('Error submitting feedback:', error);
      toast.error('Failed to submit feedback');
    }
  };

  const renderMessageContent = (message) => {
    if (message.response_type === 'structured_data' && message.response_data) {
      return (
        <div className="space-y-4">
          <p className="whitespace-pre-wrap">{message.content}</p>
          {message.response_data.amazon_products && message.response_data.amazon_products.length > 0 && (
            <div className="bg-gray-50 rounded-lg p-4">
              <h4 className="font-medium text-gray-900 mb-2 flex items-center">
                <Star className="h-4 w-4 mr-2 text-yellow-500" />
                Amazon Products
              </h4>
              <div className="space-y-2">
                {message.response_data.amazon_products.map((product, index) => (
                  <div key={index} className="flex items-center justify-between text-sm">
                    <span className="text-gray-700">{product.title}</span>
                    <div className="flex items-center">
                      <span className="text-yellow-500 mr-1">{product.rating}⭐</span>
                      <span className="text-gray-500">({product.reviews} reviews)</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
          {message.response_data.instacart_products && message.response_data.instacart_products.length > 0 && (
            <div className="bg-gray-50 rounded-lg p-4">
              <h4 className="font-medium text-gray-900 mb-2 flex items-center">
                <Package className="h-4 w-4 mr-2 text-blue-500" />
                Instacart Products
              </h4>
              <div className="space-y-2">
                {message.response_data.instacart_products.map((product, index) => (
                  <div key={index} className="text-sm text-gray-700">
                    {product.name}
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      );
    }

    if (message.response_type === 'chart' && message.response_data) {
      return (
        <div className="space-y-4">
          <p className="whitespace-pre-wrap">{message.content}</p>
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center mb-2">
              <BarChart3 className="h-4 w-4 mr-2 text-blue-500" />
              <span className="font-medium text-gray-900">Data Visualization</span>
            </div>
            <p className="text-sm text-gray-600">Chart data available for detailed analysis</p>
          </div>
        </div>
      );
    }

    if (message.response_type === 'recommendation_list' && message.response_data) {
      return (
        <div className="space-y-4">
          <p className="whitespace-pre-wrap">{message.content}</p>
          {message.response_data.recommendations && (
            <div className="bg-gray-50 rounded-lg p-4">
              <h4 className="font-medium text-gray-900 mb-2 flex items-center">
                <TrendingUp className="h-4 w-4 mr-2 text-green-500" />
                Recommendations
              </h4>
              <div className="space-y-2">
                {message.response_data.recommendations.map((rec, index) => (
                  <div key={index} className="flex items-center justify-between text-sm">
                    <span className="text-gray-700">{rec.title}</span>
                    <span className="text-green-600 font-medium">
                      {Math.round(rec.recommendation_score * 100)}%
                    </span>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      );
    }

    return <p className="whitespace-pre-wrap">{message.content}</p>;
  };

  return (
    <div className="flex flex-col h-[calc(100vh-8rem)] bg-white rounded-lg shadow-sm border border-gray-200">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200">
        <div className="flex items-center">
          <MessageSquare className="h-6 w-6 text-blue-600 mr-3" />
          <div>
            <h1 className="text-lg font-semibold text-gray-900">AI Assistant</h1>
            <p className="text-sm text-gray-600">Inventory & Product Analysis</p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <div className="w-2 h-2 bg-green-500 rounded-full"></div>
          <span className="text-sm text-gray-600">Online</span>
        </div>
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {messages.map((message) => (
          <div
            key={message.id}
            className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
          >
            <div className={`flex max-w-[80%] ${message.type === 'user' ? 'flex-row-reverse' : 'flex-row'}`}>
              {/* Avatar */}
              <div className={`flex-shrink-0 ${message.type === 'user' ? 'ml-3' : 'mr-3'}`}>
                <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                  message.type === 'user' 
                    ? 'bg-blue-600 text-white' 
                    : message.isError 
                    ? 'bg-red-100 text-red-600'
                    : 'bg-gray-100 text-gray-600'
                }`}>
                  {message.type === 'user' ? <User className="h-4 w-4" /> : <Bot className="h-4 w-4" />}
                </div>
              </div>

              {/* Message Content */}
              <div className={`rounded-lg p-3 ${
                message.type === 'user'
                  ? 'bg-blue-600 text-white'
                  : message.isError
                  ? 'bg-red-50 text-red-900 border border-red-200'
                  : 'bg-gray-50 text-gray-900'
              }`}>
                {renderMessageContent(message)}
                
                {/* Suggestions */}
                {message.suggestions && (
                  <div className="mt-3 space-y-2">
                    <p className="text-sm font-medium text-gray-700">Try asking:</p>
                    <div className="flex flex-wrap gap-2">
                      {message.suggestions.map((suggestion, index) => (
                        <button
                          key={index}
                          onClick={() => handleSuggestionClick(suggestion)}
                          className="px-3 py-1 bg-white border border-gray-300 rounded-full text-sm text-gray-700 hover:bg-gray-50 transition-colors"
                        >
                          {suggestion}
                        </button>
                      ))}
                    </div>
                  </div>
                )}

                {/* Message Info */}
                <div className="flex items-center justify-between mt-2 text-xs opacity-75">
                  <span>{message.timestamp.toLocaleTimeString()}</span>
                  {message.confidence && (
                    <span>Confidence: {Math.round(message.confidence * 100)}%</span>
                  )}
                </div>

                {/* Feedback Buttons */}
                {message.type === 'bot' && !message.isError && message.id !== 'welcome' && (
                  <div className="flex items-center space-x-2 mt-2">
                    <button
                      onClick={() => handleFeedback(message.id, true)}
                      disabled={message.feedback}
                      className={`p-1 rounded transition-colors ${
                        message.feedback === 'positive'
                          ? 'text-green-600 bg-green-100'
                          : 'text-gray-400 hover:text-green-600 hover:bg-green-50'
                      }`}
                    >
                      <ThumbsUp className="h-3 w-3" />
                    </button>
                    <button
                      onClick={() => handleFeedback(message.id, false)}
                      disabled={message.feedback}
                      className={`p-1 rounded transition-colors ${
                        message.feedback === 'negative'
                          ? 'text-red-600 bg-red-100'
                          : 'text-gray-400 hover:text-red-600 hover:bg-red-50'
                      }`}
                    >
                      <ThumbsDown className="h-3 w-3" />
                    </button>
                  </div>
                )}
              </div>
            </div>
          </div>
        ))}

        {/* Loading Indicator */}
        {isLoading && (
          <div className="flex justify-start">
            <div className="flex mr-3">
              <div className="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center">
                <Bot className="h-4 w-4 text-gray-600" />
              </div>
            </div>
            <div className="bg-gray-50 rounded-lg p-3">
              <div className="flex items-center space-x-2">
                <Loader className="h-4 w-4 animate-spin text-gray-600" />
                <span className="text-gray-600">Thinking...</span>
              </div>
            </div>
          </div>
        )}

        <div ref={messagesEndRef} />
      </div>

      {/* Input */}
      <div className="p-4 border-t border-gray-200">
        <div className="flex items-center space-x-3">
          <input
            type="text"
            value={inputMessage}
            onChange={(e) => setInputMessage(e.target.value)}
            onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
            placeholder="Ask me about products, reviews, forecasts, or inventory..."
            disabled={isLoading}
            className="flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:opacity-50"
          />
          <button
            onClick={() => handleSendMessage()}
            disabled={!inputMessage.trim() || isLoading}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            <Send className="h-4 w-4" />
          </button>
        </div>
      </div>
    </div>
  );
};

export default ChatbotInterface;