import axios from 'axios';
import toast from 'react-hot-toast';

// Create axios instance
const api = axios.create({
  baseURL: process.env.REACT_APP_API_URL || 'http://localhost:5000/api',
  timeout: 10000,
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bear<PERSON> ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle errors
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    if (error.response?.status === 401) {
      // Token expired or invalid
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      window.location.href = '/login';
    } else if (error.response?.status >= 500) {
      toast.error('Server error. Please try again later.');
    } else if (error.response?.data?.error) {
      toast.error(error.response.data.error);
    } else if (error.message === 'Network Error') {
      toast.error('Network error. Please check your connection.');
    }
    return Promise.reject(error);
  }
);

// Auth API
export const authAPI = {
  login: (credentials) => api.post('/auth/login', credentials),
  register: (userData) => api.post('/auth/register', userData),
  getProfile: () => api.get('/auth/me'),
  updateProfile: (data) => api.put('/auth/profile', data),
  changePassword: (data) => api.put('/auth/change-password', data),
  logout: () => api.post('/auth/logout'),
};

// Products API
export const productsAPI = {
  getAll: (params = {}) => api.get('/products', { params }),
  getById: (id) => api.get(`/products/${id}`),
  create: (data) => api.post('/products', data),
  update: (id, data) => api.put(`/products/${id}`, data),
  delete: (id) => api.delete(`/products/${id}`),
  getLowStock: () => api.get('/products/alerts/low-stock'),
  adjustStock: (id, data) => api.post(`/products/${id}/adjust-stock`, data),
};

// Orders API
export const ordersAPI = {
  getAll: (params = {}) => api.get('/orders', { params }),
  getById: (id) => api.get(`/orders/${id}`),
  create: (data) => api.post('/orders', data),
  update: (id, data) => api.put(`/orders/${id}`, data),
  updateStatus: (id, data) => api.put(`/orders/${id}/status`, data),
  delete: (id) => api.delete(`/orders/${id}`),
  getStats: (params = {}) => api.get('/orders/stats/overview', { params }),
};

// Suppliers API
export const suppliersAPI = {
  getAll: (params = {}) => api.get('/suppliers', { params }),
  getById: (id) => api.get(`/suppliers/${id}`),
  create: (data) => api.post('/suppliers', data),
  update: (id, data) => api.put(`/suppliers/${id}`, data),
  delete: (id) => api.delete(`/suppliers/${id}`),
  getProducts: (id) => api.get(`/suppliers/${id}/products`),
  updateRating: (id, data) => api.put(`/suppliers/${id}/rating`, data),
  getStats: () => api.get('/suppliers/stats/overview'),
};

// Users API
export const usersAPI = {
  getAll: (params = {}) => api.get('/users', { params }),
  getById: (id) => api.get(`/users/${id}`),
  create: (data) => api.post('/users', data),
  update: (id, data) => api.put(`/users/${id}`, data),
  delete: (id) => api.delete(`/users/${id}`),
  updateStatus: (id, data) => api.put(`/users/${id}/status`, data),
  resetPassword: (id, data) => api.put(`/users/${id}/reset-password`, data),
  getStats: () => api.get('/users/stats/overview'),
};

// Dashboard API
export const dashboardAPI = {
  getStats: (params = {}) => api.get('/dashboard/stats', { params }),
  getSalesChart: (params = {}) => api.get('/dashboard/charts/sales', { params }),
  getInventoryChart: () => api.get('/dashboard/charts/inventory'),
  getRecentOrders: (params = {}) => api.get('/dashboard/recent-orders', { params }),
  getLowStockAlerts: (params = {}) => api.get('/dashboard/low-stock-alerts', { params }),
  getTopProducts: (params = {}) => api.get('/dashboard/top-products', { params }),
  getAlerts: () => api.get('/dashboard/alerts'),
};

// Reports API
export const reportsAPI = {
  getSalesReport: (params = {}) => api.get('/reports/sales', { params }),
  getInventoryReport: (params = {}) => api.get('/reports/inventory', { params }),
  getTopSellingProducts: (params = {}) => api.get('/reports/products/top-selling', { params }),
  getSupplierPerformance: (params = {}) => api.get('/reports/suppliers/performance', { params }),
  getProfitAnalysis: (params = {}) => api.get('/reports/profit-analysis', { params }),
  export: (params = {}) => api.get('/reports/export', {
    params,
    responseType: params.format === 'pdf' ? 'blob' : 'text'
  }),
};

// Instacart API
export const instacartAPI = {
  getSummary: () => api.get('/instacart/analytics/summary'),
  getTopProducts: (limit = 10) => api.get(`/instacart/analytics/top-products?limit=${limit}`),
  getOrdersByHour: () => api.get('/instacart/analytics/orders-by-hour'),
  getOrdersByDow: () => api.get('/instacart/analytics/orders-by-dow'),
  getDepartmentStats: () => api.get('/instacart/analytics/department-stats'),
  getAisleStats: (limit = 20) => api.get(`/instacart/analytics/aisle-stats?limit=${limit}`),
  getProducts: (params = {}) => api.get('/instacart/products', { params }),
  getOrders: (params = {}) => api.get('/instacart/orders', { params }),
  getAisles: () => api.get('/instacart/aisles'),
  getDepartments: () => api.get('/instacart/departments')
};

// Predictive Analytics API
export const predictiveAPI = {
  // Demand Forecasting
  generateForecasts: (data) => api.post('/predictive/forecasts/generate', data),
  getForecasts: (params = {}) => api.get('/predictive/forecasts', { params }),
  getForecast: (id) => api.get(`/predictive/forecasts/${id}`),
  getProductForecast: (productId, params = {}) => api.get(`/predictive/forecasts/product/${productId}`, { params }),

  // Reorder Suggestions
  generateReorderSuggestions: (data) => api.post('/predictive/reorder-suggestions/generate', data),
  getReorderSuggestions: (params = {}) => api.get('/predictive/reorder-suggestions', { params }),
  getHighPrioritySuggestions: (limit = 10) => api.get(`/predictive/reorder-suggestions/high-priority?limit=${limit}`),
  updateReorderSuggestion: (id, data) => api.patch(`/predictive/reorder-suggestions/${id}`, data),

  // Velocity Analytics
  getVelocityAnalytics: (params = {}) => api.get('/predictive/velocity/analytics', { params }),

  // Dashboard
  getDashboardSummary: () => api.get('/predictive/dashboard/summary'),

  // Scheduler Management
  getSchedulerStatus: () => api.get('/predictive/scheduler/status'),
  triggerSchedulerJob: (jobName) => api.post(`/predictive/scheduler/trigger/${jobName}`),

  // Data Synchronization
  getSyncStatus: () => api.get('/predictive/sync/status'),
  syncProducts: () => api.post('/predictive/sync/products'),
  syncVelocity: () => api.post('/predictive/sync/velocity'),
  fullSync: () => api.post('/predictive/sync/full')
};

// Amazon Reviews API
export const amazonReviewsAPI = {
  // Import and Processing
  importReviews: (data) => api.post('/amazon-reviews/import', data),
  processText: (data) => api.post('/amazon-reviews/process-text', data),
  generateInsights: (productId) => api.post(`/amazon-reviews/generate-insights/${productId}`),
  
  // Reviews
  getReviews: (params = {}) => api.get('/amazon-reviews/reviews', { params }),
  getReview: (reviewId) => api.get(`/amazon-reviews/reviews/${reviewId}`),
  getProductReviews: (productId, params = {}) => api.get(`/amazon-reviews/reviews/product/${productId}`, { params }),
  getUserReviews: (userId, params = {}) => api.get(`/amazon-reviews/reviews/user/${userId}`, { params }),
  
  // Products
  getProducts: (params = {}) => api.get('/amazon-reviews/products', { params }),
  getProduct: (productId) => api.get(`/amazon-reviews/products/${productId}`),
  searchProducts: (query, params = {}) => api.get(`/amazon-reviews/products/search/${query}`, { params }),
  getTopRatedProducts: (params = {}) => api.get('/amazon-reviews/products/top-rated', { params }),
  getProductsByCategory: (category, params = {}) => api.get(`/amazon-reviews/products/category/${category}`, { params }),
  getRecommendations: (productId, params = {}) => api.get(`/amazon-reviews/products/${productId}/recommendations`, { params }),
  
  // Analytics
  getSentimentAnalysis: (productId) => api.get(`/amazon-reviews/sentiment/${productId}`),
  getKeywords: (productId, params = {}) => api.get(`/amazon-reviews/keywords/${productId}`, { params }),
  getDashboardAnalytics: () => api.get('/amazon-reviews/analytics/dashboard'),
  getProcessingStatus: () => api.get('/amazon-reviews/status')
};

// Chatbot API
export const chatbotAPI = {
  // Query Processing
  processQuery: (data) => api.post('/chatbot/query', data),
  
  // History
  getSessionHistory: (sessionId, params = {}) => api.get(`/chatbot/sessions/${sessionId}/history`, { params }),
  getUserHistory: (userId, params = {}) => api.get(`/chatbot/users/${userId}/history`, { params }),
  
  // Feedback
  submitFeedback: (queryId, data) => api.post(`/chatbot/queries/${queryId}/feedback`, data),
  
  // Analytics
  getAnalytics: (params = {}) => api.get('/chatbot/analytics', { params }),
  getPopularQueries: (params = {}) => api.get('/chatbot/analytics/popular-queries', { params }),
  getFailedQueries: (params = {}) => api.get('/chatbot/analytics/failed-queries', { params }),
  getQueryTypeAnalytics: (params = {}) => api.get('/chatbot/analytics/query-types', { params }),
  getPerformanceMetrics: (params = {}) => api.get('/chatbot/analytics/performance', { params }),
  
  // Queries Management
  getQueries: (params = {}) => api.get('/chatbot/queries', { params }),
  getQuery: (queryId) => api.get(`/chatbot/queries/${queryId}`),
  
  // Dashboard
  getDashboardSummary: () => api.get('/chatbot/dashboard/summary'),
  
  // Health Check
  getHealth: () => api.get('/chatbot/health')
};

// Utility functions
export const handleApiError = (error) => {
  if (error.response?.data?.errors) {
    // Validation errors
    return error.response.data.errors.map(err => err.msg).join(', ');
  } else if (error.response?.data?.error) {
    return error.response.data.error;
  } else if (error.message) {
    return error.message;
  } else {
    return 'An unexpected error occurred';
  }
};

export const downloadFile = async (url, filename) => {
  try {
    const response = await api.get(url, {
      responseType: 'blob',
    });
    
    const blob = new Blob([response.data]);
    const downloadUrl = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = downloadUrl;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    link.remove();
    window.URL.revokeObjectURL(downloadUrl);
  } catch (error) {
    toast.error('Failed to download file');
    throw error;
  }
};

export default api;
